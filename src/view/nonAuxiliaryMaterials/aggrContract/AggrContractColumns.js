import { h, reactive, ref } from 'vue'
import { Tag, Button, message } from 'ant-design-vue'
import {baseColumns} from "@/view/common/baseColumns";
import { productClassify } from '@/view/common/constant'
import {useColumnsRender} from "../../common/useColumnsRender";
import { GlobalIcon } from '@/components/icon/index.js'
import { useMerchant } from '@/view/common/useMerchant'
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()
const { merchantOptions, getMerchantOptions } = useMerchant()

// 初始化获取客商数据
await getMerchantOptions()

/**
 * 表格点击编辑/查看按钮
 */
function operationEdit(type) {
  return type === 'edit' && !operationEditShow()
    ? {display: 'none'}
    : {marginLeft: type === 'view' && operationEditShow() ? '15px' : '0'}
}


/**
 * 是否显示编辑标签
 * @returns {boolean}
 */
function operationEditShow() {
  // 根据实际情况返回是否显示编辑按钮的状态
  return true // 示例中总是返回 true
}



function getColumns() {
  const commColumns = reactive([
    'businessType', // 业务类型
    'contractNo',      // 购销合同
    'contractYear',    // 购销年份
    'businessDistinction',    // 业务区分
    'versionNo',   // 版本号
    'createrUser',  // 制单人
    'createrTime',  // 制单时间
    'status',      // 单据状态

  ])

  // 导出字段设置
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      title: '操作',
      maxWidth: 80,
      width: 80,
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
      resizable:"true",
     /* customRender: ({ record }) => {
        // 根据状态控制按钮禁用
        const isConfirmed = record.status === '1';
        return {
          children: h('div', {
            class: 'operation-container'
          }, [
            h(Button, {
              size: 'small',
              type: 'link',
              onClick: (event) => {
                event.stopPropagation(); // 阻止事件冒泡
                if (isConfirmed) {
                  message.warning('确认状态的数据不允许编辑'); // 弹出警告
                  return; // 阻止进入编辑页面
                }
                handleEditByRow(record); // 进入编辑页面
              },
              style: operationEdit('edit', record) // 传递 record
            }, {
              icon: () => h(GlobalIcon, {
                type: 'form',
                style: { color: isConfirmed ? '#999' : '#e93f41' }
              })
            })
          ])
        }
      }*/
    },
    {
      title: '业务类型',
      width: 135,
      minWidth: 135,
      align: 'center',
      dataIndex: 'businessType',
      key: 'businessType',
      resizable:"true",
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.businessType))
      }
    },
    {
      title: '合同号',
      width: 120,
      minWidth: 120,
      align: 'center',
      dataIndex: 'contractNo',
      resizable:"true",
      key: 'contractNo'
    },{
      title: '供应商',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'supplier',
      resizable:"true",
      key: 'supplier',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, merchantOptions.value))
      }
    },{
      title: '国内委托方',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'domesticPrincipal',
      resizable:"true",
      key: 'domesticPrincipal',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, merchantOptions.value))
      }
    },
    {
      title: '签约日期',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'signingDate',
      key: 'signingDate',
      resizable:"true",
      customRender: ({ text }) => {
        // 如果有值，格式化为yyyy-mm-dd格式
        if (text && typeof text === 'string') {
          // 如果已经是yyyy-mm-dd格式，直接返回
          if (text.match(/^\d{4}-\d{2}-\d{2}$/)) {
            return text;
          }
          // 尝试解析日期并格式化为yyyy-mm-dd
          const date = new Date(text);
          if (!isNaN(date.getTime())) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
          }
        }
        return text;
      }
    },
    {
      title: '金额',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'totalAmount',
      resizable:"true",
      key: 'totalAmount'
    },{
      title: '生效日期',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'effectiveDate',
      key: 'effectiveDate',
      resizable:"true",
      customRender: ({ text }) => {
        // 如果有值，格式化为yyyy-mm-dd格式
        if (text && typeof text === 'string') {
          // 如果已经是yyyy-mm-dd格式，直接返回
          if (text.match(/^\d{4}-\d{2}-\d{2}$/)) {
            return text;
          }
          // 尝试解析日期并格式化为yyyy-mm-dd
          const date = new Date(text);
          if (!isNaN(date.getTime())) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
          }
        }
        return text;
      }
    },
    {
      title: '版本号',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'versionNo',
      resizable:"true",
      key: 'versionNo'
    },{
      title: '制单人',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'createrUser',
      resizable:"true",
      key: 'createrUser'
    },
    {
      title: '制单时间',
      width: 135,
      minWidth: 135,
      align: 'center',
      dataIndex: 'createrTime',
      resizable:"true",
      key: 'createrTime'
    },
    {
      title: '单据状态',
      width: 80,
      minWidth: 80,
      align: 'center',
      dataIndex: 'status',
      resizable:"true",
      key: 'status',
      customRender: ({ text }) => {
        const tagColor = text === '2' ? 'error' : 'success';
        return h(Tag, { color: tagColor }, cmbShowRender(text, productClassify.data_status))
      }
    },

  ])
  return {
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}

export { getColumns }
